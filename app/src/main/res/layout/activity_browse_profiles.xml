<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_browse_profiles"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:ignore="ContentDescription">

    <include
        android:id="@+id/tool_bar_browse_profile"
        layout="@layout/home_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="4dp"
        app:elevation="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/browse_city_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="24dp"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:text="@string/browsing_in"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/browse_city_with_icon"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tool_bar_browse_profile" />

    <TextView
        android:id="@+id/browse_city_with_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="24dp"
        android:fontFamily="@font/inter_bold"
        android:textColor="@color/color_primary"
        android:textSize="16sp"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_love_heart_pin_small"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        android:drawablePadding="2dp"
        app:layout_constraintStart_toEndOf="@+id/browse_city_text"
        app:layout_constraintTop_toBottomOf="@+id/tool_bar_browse_profile" />

    <ScrollView
        android:id="@+id/view_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="36dp"
        app:layout_constraintBottom_toTopOf="@+id/player"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/browse_city_text">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/group3_constraint_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/profile_men_seactivity_group3_constraint_layout_margin_start"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="20dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/genres_view_recycler_view">

                <TextView
                    android:id="@+id/profession_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_profession_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_profession_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_profession_text_view_text_size"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/user_height1_text_view"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="63dp" />

                <TextView
                    android:id="@+id/user_profession_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_product_designer_text_view_margin_top"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_product_designer_text_view_text_size"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/profession_text_view" />

                <TextView
                    android:id="@+id/age_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_age_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_height_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_age_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="-1dp" />

                <TextView
                    android:id="@+id/user_location_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="end"
                    android:lineSpacingMultiplier="1.1"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_age_text_view_text_size"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <ImageView
                    android:id="@+id/location_image_view"
                    android:layout_width="12dp"
                    android:layout_height="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="4dp"
                    android:gravity="end"
                    android:scaleType="fitXY"
                    android:src="@drawable/ic_location_icon"
                    app:layout_constraintEnd_toStartOf="@+id/user_location_text_view"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/color_primary" />

                <TextView
                    android:id="@+id/distance_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter"
                    android:gravity="end"
                    android:layout_marginTop="2dp"
                    android:lineSpacingMultiplier="1.09"
                    android:textColor="@color/grey2"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/location_image_view" />

                <TextView
                    android:id="@+id/user_height1_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_yrs_text_view_margin_top"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_yrs_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/age_text_view" />

                <TextView
                    android:id="@+id/user_education_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_university_of_new_ha_text_view_margin_top"
                    android:layout_marginEnd="18dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_university_of_new_ha_text_view_text_size"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/education_text_view" />

                <TextView
                    android:id="@+id/user_home_town_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text=""
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_san_mateo_ca_text_view_text_size"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/hometown_text_view" />

                <TextView
                    android:id="@+id/education_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/profile_men_seactivity_education_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_education_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_education_text_view_text_size"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/user_profession_text_view"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="126dp" />

                <TextView
                    android:id="@+id/hometown_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/profile_men_seactivity_hometown_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/profile_men_seactivity_hometown_text_view_text_size"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/user_education_text_view" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/group10_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_second_photo_image_view">

                <ImageView
                    android:id="@+id/boots_bottom_image_view"
                    android:layout_width="108dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="10dp"
                    android:elevation="5dp"
                    android:scaleType="fitXY"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/user_match_boot_preview_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.03"
                    android:textColor="@color/grey2"
                    android:textSize="24sp"
                    app:layout_constraintStart_toStartOf="@+id/boots_bottom_image_view"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/boots_bottom_image_view" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/user_second_photo_image_view"
                android:layout_width="0dp"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/profilecard_blank_photo_copy_constraint_layout" />

            <ImageView
                android:id="@+id/user_third_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/profilecard_blank_photo_copy3_constraint_layout" />

            <ImageView
                android:id="@+id/user_fourth_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_third_photo_image_view" />

            <ImageView
                android:id="@+id/user_fifth_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_fourth_photo_image_view" />

            <ImageView
                android:id="@+id/user_sixth_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_fifth_photo_image_view" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/profilecard_blank_photo_copy_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/group3_constraint_layout">

                <ImageView
                    android:id="@+id/rectangle_image_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/grey5"
                    android:scaleType="centerCrop"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@color/grey5"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <TextView
                    android:id="@+id/ice_breaker_1_question_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_how_do_you_describe_text_view_text_size"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/copy_image_view" />

                <TextView
                    android:id="@+id/ice_breaker_1_answer_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="26dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.03"
                    android:textColor="@color/grey2"
                    android:textSize="18sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ice_breaker_1_question_text_view"
                    app:layout_constraintVertical_bias="0" />

                <ImageView
                    android:id="@+id/copy_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/profile_men_seactivity_copy_image_view_margin_start"
                    android:layout_marginTop="24dp"
                    android:scaleType="center"
                    android:src="@drawable/ic_quote"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/profilecard_blank_photo_copy3_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/browse_profiles_card_item_spacing"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/group10_constraint_layout">

                <ImageView
                    android:id="@+id/rectangle_two_image_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/grey5"
                    android:scaleType="centerCrop"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/user_second_ice_breaker_photo_image_view"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="0dp" />

                <View
                    android:id="@+id/user_second_ice_breaker_photo_image_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@color/grey5"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <TextView
                    android:id="@+id/ice_breaker_2_question_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/profile_men_seactivity_how_do_you_describe_two_text_view_text_size"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/copy_two_image_view" />

                <TextView
                    android:id="@+id/ice_breaker_2_answer_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="26dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.03"
                    android:textColor="@color/grey2"
                    android:textSize="18sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ice_breaker_2_question_text_view"
                    app:layout_constraintVertical_bias="0" />

                <ImageView
                    android:id="@+id/copy_two_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/profile_men_seactivity_copy_two_image_view_margin_start"
                    android:layout_marginTop="24dp"
                    android:scaleType="center"
                    android:src="@drawable/ic_quote"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="18dp"
                    tools:layout_editor_absoluteY="57dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/user_first_photo_image_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/browse_profiles_image_height"
                android:layout_marginTop="11dp"
                android:background="@color/grey5"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_age1_text_view" />

            <TextView
                android:id="@+id/favorites_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:text="Favorites"
                android:textColor="@color/grey1"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_first_photo_image_view" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/favorites_view_recycler_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/favorites_text_view"
                tools:listitem="@layout/item_favorite_profile" />

            <TextView
                android:id="@+id/genres_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:text="Genres"
                android:textColor="@color/grey1"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/favorites_view_recycler_view" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/genres_view_recycler_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/genres_text_view"
                tools:listitem="@layout/item_genres" />

            <TextView
                android:id="@+id/user_name_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="@dimen/profile_men_seactivity_jon_text_view_margin_top"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:lineSpacingMultiplier="1.09"
                android:text=""
                android:textColor="@color/grey1"
                android:textSize="22sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:layout_editor_absoluteX="19dp"
                tools:layout_editor_absoluteY="10dp" />

            <android.widget.Button
                android:id="@+id/report_user_button"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="wrap_content"
                android:paddingStart="36dp"
                android:paddingEnd="36dp"
                android:layout_height="@dimen/profile_men_seactivity_button_ghost_button_height"
                android:layout_marginStart="48dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="48dp"
                android:background="@drawable/button_border_blue"
                android:text="@string/profile_men_seactivity_button_ghost_button_text"
                android:textColor="@color/color_primary"
                android:theme="@style/BottomCTAButton"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_sixth_photo_image_view" />

            <TextView
                android:id="@+id/user_age1_text_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="@dimen/profile_men_seactivity_group5_constraint_layout_margin_top"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:text=""
                android:textColor="@color/grey1"
                android:textSize="20sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_name_text_view" />

            <ImageView
                android:id="@+id/height_verified_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginBottom="6dp"
                android:gravity="start"
                android:src="@drawable/ic_height_verified_badge"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/user_age1_text_view"
                app:layout_constraintStart_toEndOf="@+id/user_age1_text_view" />

            <TextView
                android:id="@+id/height_verified_text_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginBottom="6dp"
                android:fontFamily="@font/inter"
                android:gravity="start"
                android:textColor="@color/grey3"
                android:textSize="14sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/user_age1_text_view"
                app:layout_constraintStart_toEndOf="@+id/height_verified_badge" />

            <ImageView
                android:id="@+id/top_heels_icon"
                android:layout_width="48dp"
                android:layout_height="32dp"
                android:layout_marginEnd="18dp"
                android:layout_marginBottom="8dp"
                android:elevation="5dp"
                android:scaleType="fitXY"
                android:visibility="invisible"
                app:layout_constraintBottom_toTopOf="@+id/user_first_photo_image_view"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <FrameLayout
        android:id="@+id/like_dislike_frame"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/player">

        <android.widget.Button
            android:id="@+id/dislike_button"
            android:layout_width="84dp"
            android:layout_height="84dp"
            android:layout_gravity="start"
            android:layout_marginStart="20dp"
            android:background="@drawable/ic_pass"
            android:elevation="6dp" />

        <android.widget.Button
            android:id="@+id/dm_button"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/dialog_input_background"
            android:minWidth="180dp"
            android:fontFamily="@font/inter_bold"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Message"
            android:textAllCaps="false"
            android:textColor="@color/color_primary"
            android:textSize="18sp" />

        <android.widget.Button
            android:id="@+id/like_button"
            android:layout_width="84dp"
            android:layout_height="84dp"
            android:layout_gravity="end"
            android:layout_marginEnd="20dp"
            android:background="@drawable/ic_like"
            android:elevation="6dp" />
    </FrameLayout>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/user_like_dislike_animation_view"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:layout_marginTop="50dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tool_bar_browse_profile"
        app:layout_constraintBottom_toBottomOf="@+id/like_dislike_frame"
        app:lottie_autoPlay="true"
        app:lottie_loop="false" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="62dp"
            android:layout_height="62dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/progressTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="54dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="54dp"
            android:gravity="center"
            android:text="We're searching the clouds for that perfect match..."
            android:textColor="#646C70"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/layout_out_of_people"
        layout="@layout/fragment_blank_out_of_people"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/browse_city_text" />

    <include
        android:id="@+id/layout_enable_location"
        layout="@layout/fragment_blank_enable_location"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tool_bar_browse_profile" />

    <include
        android:id="@+id/user_error"
        layout="@layout/layout_user_error_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tool_bar_browse_profile" />

    <FrameLayout
        android:id="@+id/locations_container"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/get_users_api_error_container"
        layout="@layout/get_nearby_users_api_error_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tool_bar_browse_profile" />

    <com.lovebeats.customViews.PlayerView
        android:id="@+id/player"
        android:layout_width="0dp"
        android:layout_height="54dp"
        android:background="@color/color_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>